import pandas as pd
import matplotlib.pyplot as plt
import os
import seaborn as sns

def plot_comprehensive_attack_analysis():
    """
    Generates a comprehensive analysis plot for a victim UE (NormalUE-0)
    under attack, showing SINR and RSRP from different sources, as well as
    the overall Packet Delivery Ratio (PDR).
    """
    # --- 1. File Paths ---
    script_dir = os.path.dirname(os.path.abspath(__file__))
    base_dir = os.path.join(script_dir, '..', '..')
    data_path = os.path.join(
        base_dir,
        'output',
        'nr_v2x_simulator_simulation_results',
        'simulation_1',
        'NormalUE-1.csv'
    )
    output_dir = os.path.join(script_dir)
    output_filename = 'simulation_1_comprehensive_analysis.png'
    output_path = os.path.join(output_dir, output_filename)

    os.makedirs(output_dir, exist_ok=True)

    # --- 2. Data Loading and Preprocessing ---
    try:
        df_raw = pd.read_csv(data_path)
    except FileNotFoundError:
        print(f"Error: Data file not found at {data_path}")
        return

    # Filter for PSSCH reception events, which contain all necessary data
    df = df_raw[df_raw['EventType'] == 'PSSCH_RX_OUTCOME'].copy()

    # Convert data types and handle missing values
    df['Time (s)'] = pd.to_numeric(df['Time_ms'], errors='coerce') / 1000
    df['SINR_dB'] = pd.to_numeric(df['SINR_dB'], errors='coerce')
    df['RSRP_dBm'] = pd.to_numeric(df['RSRP_dBm'], errors='coerce')
    df.dropna(subset=['Time (s)', 'Related_UE_ID', 'SINR_dB', 'RSRP_dBm'], inplace=True)
    df.sort_values(by='Time (s)', inplace=True)

    # Calculate PDR
    # df['is_success'] = (df['Outcome'] == 'SUCCESS').astype(int)
    # window_size = 50
    # df['pdr'] = df['is_success'].rolling(window=window_size, min_periods=1).mean()

    # --- 3. Plotting ---
    plt.style.use('seaborn-v0_8-whitegrid')
    # fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(16, 18), sharex=True)
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(22, 16), sharex=True)
    fig.suptitle('Comprehensive Attack Analysis on Victim UE (NormalUE-0)', fontsize=18, weight='bold')

    # Create a color palette for the different UEs
    unique_ues = sorted(df['Related_UE_ID'].unique())
    palette = sns.color_palette('husl', n_colors=len(unique_ues))
    color_map = dict(zip(unique_ues, palette))

    # Plot data for each source UE on the first two subplots
    for ue_id in unique_ues:
        ue_df = df[df['Related_UE_ID'] == ue_id]
        color = color_map[ue_id]
        label = f"From {ue_id}"
        print(ue_df)

        # --- Subplot 1: SINR ---
        ax1.plot(ue_df['Time (s)'], ue_df['SINR_dB'], marker='o', linestyle='-',
                 markersize=4, alpha=0.8, label=label, color=color)

        # --- Subplot 2: RSRP ---
        ax2.plot(ue_df['Time (s)'], ue_df['RSRP_dBm'], marker='x', linestyle='--',
                 markersize=4, alpha=0.8, label=label, color=color)

    # # --- Subplot 3: Packet Delivery Ratio (PDR) ---
    # ax3.plot(df['Time (s)'], df['pdr'], color='blue', linewidth=2,
    #          label=f'PDR ({window_size}-Packet Rolling Average)')

    # --- 4. Finalizing and Saving ---
    # Formatting for SINR plot
    ax1.set_title('SINR of Received Packets by Source', fontsize=14)
    ax1.set_ylabel('SINR (dB)', fontsize=12)
    ax1.legend(title="Source UE", bbox_to_anchor=(1.02, 1), loc='upper left')
    ax1.grid(True, which='both', linestyle='--', linewidth=0.5)

    # Formatting for RSRP plot
    ax2.set_title('RSRP of Received Packets by Source', fontsize=14)
    ax2.set_ylabel('RSRP (dBm)', fontsize=12)
    ax2.legend(title="Source UE", bbox_to_anchor=(1.02, 1), loc='upper left')
    ax2.grid(True, which='both', linestyle='--', linewidth=0.5)

    # Formatting for PDR plot
    # ax3.set_title('Victim UE Packet Delivery Ratio Over Time', fontsize=14)
    # ax3.set_xlabel('Time (s)', fontsize=12)
    # ax3.set_ylabel('Packet Delivery Ratio', fontsize=12)
    # ax3.set_ylim(0, 1.05)
    # ax3.legend(bbox_to_anchor=(1.02, 1), loc='upper left')
    # ax3.grid(True, which='both', linestyle='--', linewidth=0.5)

    plt.tight_layout(rect=[0, 0, 0.88, 0.96]) # Adjust layout
    plt.savefig(output_path, dpi=300)
    print(f"Plot saved to {output_path}")

if __name__ == '__main__':
    plot_comprehensive_attack_analysis()
