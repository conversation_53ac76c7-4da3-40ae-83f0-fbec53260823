





"""
側鏈資源耗盡攻擊 (Sidelink Resource Exhaustion Attack) - 誘騙與伏擊實現

此攻擊模型實現了一個智能的兩階段攻擊策略：
1. 誘騙階段 (Inducement Phase): 策略性地污染大部分資源，故意留下少數特定的"乾淨"資源，
   驅趕正常UE選擇這些特定資源。
2. 伏擊階段 (Ambush Phase): 基於學習到的UE行為模式，預測下一輪UE會聚集的資源區域，
   並針對性地進行高功率干擾攻擊。

攻擊邏輯：
1. **監聽與學習週期 (100ms)**: 攻擊者進入純監聽模式，收集UE行為數據，
   分析資源使用模式，建立預測模型。
2. **誘騙階段**: 基於分析結果，策略性地污染大部分資源，創造可預測的資源聚集區域。
3. **伏擊階段**: 根據學習到的模式，預測UE會選擇的資源並進行精準攻擊。
4. **適應性學習**: 持續更新預測模型，提高攻擊效果。
"""

import random
import simpy
from collections import defaultdict, deque
from src.CONFIG import *

class ResourcePoolDisruptorUE:
    def __init__(self, env, ue_id, resource_pool, comm_model, global_logger, mobility_model,
                 initial_pos=(0,0), initial_speed=0,
                 jamming_power_dbm=ATTACKER_JAMMING_POWER_DBM,
                 disruption_power_dbm=DISRUPTION_POWER_DBM,
                 ue_specific_logger=None):
        # 基本UE屬性初始化
        self.env = env
        self.ue_id = ue_id
        self.resource_pool = resource_pool
        self.comm_model = comm_model
        self.global_logger = global_logger
        self.ue_logger = ue_specific_logger
        self.mobility_model = mobility_model

        self.pos = initial_pos
        self.speed = initial_speed

        self.is_attacker = True
        self.all_other_ues = {}

        # 攻擊者特定參數
        self.jamming_power_dbm = jamming_power_dbm
        self.disruption_power_dbm = disruption_power_dbm

        # 攻擊狀態管理
        self.attack_phase = "listen"  # listen, induce, ambush
        self.is_listening = False

        # 學習與預測機制
        self.ue_behavior_history = defaultdict(lambda: deque(maxlen=PREDICTION_HISTORY_SIZE))
        self.resource_usage_patterns = defaultdict(int)
        self.predicted_target_resources = {}
        self.current_clean_resources = []

        # 監聽數據收集
        self.sensed_resource_rsrp = {}
        self.cycle_start_time = 0

        self.global_logger.log_event(Time_ms=self.env.now, EventType="RESOURCE_EXHAUSTION_INIT", UE_ID=self.ue_id,
                              Details=f"攻擊者初始化完成 - 干擾功率: {self.jamming_power_dbm}dBm, 擾亂功率: {self.disruption_power_dbm}dBm, 位置: {self.pos}")

        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=self.env.now, UE_ID=self.ue_id, EventType="ATTACKER_INIT",
                                   Details=f"資源池擾亂攻擊者啟動 - 預測歷史大小: {PREDICTION_HISTORY_SIZE}, 初始位置: {self.pos}")

        # 啟動攻擊者的運行邏輯和接收器進程
        self.action = env.process(self.attacker_run_logic())
        self.receiver_process_action = env.process(self.receiver_process())

    def attacker_run_logic(self):
        """攻擊者的主要運行邏輯，實現誘騙與伏擊循環。"""
        self.global_logger.log_event(Time_ms=self.env.now, EventType="RESOURCE_EXHAUSTION_LOGIC_START", UE_ID=self.ue_id,
                                   Details="開始執行三階段攻擊循環：監聽學習 -> 誘騙 -> 伏擊")

        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=self.env.now, UE_ID=self.ue_id, EventType="ATTACK_CYCLE_START",
                                   Details="啟動智能攻擊循環，目標：破壞資源池並預測UE行為")

        cycle_count = 0
        while True:
            cycle_count += 1
            cycle_start_time = self.env.now

            self.global_logger.log_event(Time_ms=cycle_start_time, EventType="ATTACK_CYCLE_BEGIN", UE_ID=self.ue_id,
                                       Details=f"開始第 {cycle_count} 輪攻擊循環")

            # 1. 監聽與學習階段
            yield self.env.process(self.listen_and_learn_phase())
            # 2. 誘騙階段
            yield self.env.process(self.inducement_phase())
            # 3. 伏擊階段
            yield self.env.process(self.ambush_phase())

            cycle_end_time = self.env.now
            cycle_duration = cycle_end_time - cycle_start_time

            self.global_logger.log_event(Time_ms=cycle_end_time, EventType="ATTACK_CYCLE_COMPLETE", UE_ID=self.ue_id,
                                       Details=f"第 {cycle_count} 輪攻擊循環完成，耗時: {cycle_duration}ms")

            if self.ue_logger:
                self.ue_logger.log_event(Time_ms=cycle_end_time, UE_ID=self.ue_id, EventType="CYCLE_STATS",
                                       Details=f"循環 {cycle_count} 統計 - 持續時間: {cycle_duration}ms, 學習到的模式數: {len(self.resource_usage_patterns)}")

    def listen_and_learn_phase(self):
        """監聽與學習階段: 收集UE行為數據，更新預測模型。"""
        listen_phase_start_time = self.env.now
        self.cycle_start_time = listen_phase_start_time
        self.global_logger.log_event(Time_ms=listen_phase_start_time, EventType="RESOURCE_EXHAUSTION_LISTEN_START", UE_ID=self.ue_id,
                                   Details=f"開始監聽階段，持續時間: {RESOURCE_EXHAUSTION_LISTEN_DURATION_MS}ms，目標：學習UE行為模式")
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=listen_phase_start_time, UE_ID=self.ue_id, EventType="LISTEN_LEARN_START",
                                   Details=f"進入被動監聽模式，當前位置: {self.pos}")

        # 清空先前的感知數據並開始監聽
        previous_observations = len(self.sensed_resource_rsrp)
        self.sensed_resource_rsrp.clear()
        self.is_listening = True
        self.attack_phase = "listen"

        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=self.env.now, UE_ID=self.ue_id, EventType="SENSING_RESET",
                                   Details=f"清除前次觀測數據 ({previous_observations} 筆)，開始新的監聽週期")

        # 監聽指定時間
        yield self.env.timeout(RESOURCE_EXHAUSTION_LISTEN_DURATION_MS)

        # 停止監聽並開始分析
        self.is_listening = False
        listen_phase_end_time = self.env.now
        actual_listen_duration = listen_phase_end_time - listen_phase_start_time

        # 分析收集到的數據並更新學習模型
        self._analyze_and_learn()

        self.global_logger.log_event(Time_ms=listen_phase_end_time, EventType="RESOURCE_EXHAUSTION_LISTEN_END",
                                      UE_ID=self.ue_id, Details=f"監聽階段完成 - 收集到 {len(self.sensed_resource_rsrp)} 個觀測數據，實際持續時間: {actual_listen_duration}ms")
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=listen_phase_end_time, UE_ID=self.ue_id, EventType="LISTEN_LEARN_END",
                                     Details=f"學習階段結束 - 觀測數據: {len(self.sensed_resource_rsrp)} 筆，資源使用模式: {len(self.resource_usage_patterns)} 種")

    def _analyze_and_learn(self):
        """分析監聽數據，更新UE行為預測模型。"""
        current_time = self.env.now
        analysis_start_time = current_time

        # 分析資源使用模式
        used_resources_relative = set()
        signal_strength_stats = {'min': float('inf'), 'max': float('-inf'), 'avg': 0}
        total_rsrp = 0

        for (absolute_slot, subchannel), rsrp in self.sensed_resource_rsrp.items():
            relative_slot = absolute_slot % 100
            used_resources_relative.add((relative_slot, subchannel))
            # 更新資源使用模式統計
            self.resource_usage_patterns[(relative_slot, subchannel)] += 1

            # 統計信號強度
            total_rsrp += rsrp
            signal_strength_stats['min'] = min(signal_strength_stats['min'], rsrp)
            signal_strength_stats['max'] = max(signal_strength_stats['max'], rsrp)

        if len(self.sensed_resource_rsrp) > 0:
            signal_strength_stats['avg'] = total_rsrp / len(self.sensed_resource_rsrp)
        else:
            signal_strength_stats = {'min': 0, 'max': 0, 'avg': 0}

        # 識別當前週期的乾淨資源
        all_resources = [(slot, subchannel) for slot in range(100) for subchannel in range(NUM_SUBCHANNELS)]
        current_clean_resources = [res for res in all_resources if res not in used_resources_relative]

        # 計算資源利用率
        resource_utilization = len(used_resources_relative) / len(all_resources) * 100

        # 更新UE行為歷史（簡化版本，實際可以更複雜）
        behavior_data = {
            'used_resources': used_resources_relative,
            'clean_resources': current_clean_resources,
            'total_activity': len(used_resources_relative),
            'resource_utilization': resource_utilization,
            'signal_stats': signal_strength_stats
        }
        self.ue_behavior_history[current_time].append(behavior_data)

        # 基於歷史數據預測下一輪UE可能選擇的資源
        self._predict_target_resources(current_clean_resources)

        self.global_logger.log_event(Time_ms=current_time, EventType="BEHAVIOR_ANALYSIS_COMPLETE", UE_ID=self.ue_id,
                                   Details=f"資源分析完成 - 使用率: {resource_utilization:.1f}%, 乾淨資源: {len(current_clean_resources)}, 平均信號強度: {signal_strength_stats['avg']:.1f}dBm")

        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=current_time, UE_ID=self.ue_id, EventType="LEARNING_UPDATE",
                                     Details=f"學習更新 - 乾淨資源: {len(current_clean_resources)}, 已使用: {len(used_resources_relative)}, 利用率: {resource_utilization:.1f}%, 信號範圍: {signal_strength_stats['min']:.1f}~{signal_strength_stats['max']:.1f}dBm")

    def _predict_target_resources(self, current_clean_resources):
        """基於學習到的模式預測UE下一輪可能選擇的資源。"""
        prediction_start_time = self.env.now

        # 將乾淨資源按時槽分組
        current_clean_resources_dict = {}
        for res in current_clean_resources:
            if res[0] not in current_clean_resources_dict:
                current_clean_resources_dict[res[0]] = []
            current_clean_resources_dict[res[0]].append(res[1])

        self.predicted_target_resources = current_clean_resources_dict

        # 計算預測統計
        total_predicted_slots = len(self.predicted_target_resources)
        total_predicted_resources = sum(len(subchannels) for subchannels in self.predicted_target_resources.values())

        # 分析歷史模式以提高預測準確性
        pattern_confidence = 0.0
        if len(self.resource_usage_patterns) > 0:
            # 計算模式置信度（基於歷史使用頻率的一致性）
            usage_counts = list(self.resource_usage_patterns.values())
            if len(usage_counts) > 1:
                avg_usage = sum(usage_counts) / len(usage_counts)
                variance = sum((x - avg_usage) ** 2 for x in usage_counts) / len(usage_counts)
                pattern_confidence = 1.0 / (1.0 + variance)  # 方差越小，置信度越高

        self.global_logger.log_event(Time_ms=prediction_start_time, EventType="TARGET_PREDICTION_COMPLETE", UE_ID=self.ue_id,
                                   Details=f"預測完成 - 目標時槽: {total_predicted_slots}, 總資源數: {total_predicted_resources}, 模式置信度: {pattern_confidence:.3f}")

        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=self.env.now, UE_ID=self.ue_id, EventType="PREDICTION_UPDATE",
                                     Details=f"預測目標更新 - 時槽數: {total_predicted_slots}, 資源總數: {total_predicted_resources}, 歷史模式數: {len(self.resource_usage_patterns)}, 置信度: {pattern_confidence:.3f}")

    def inducement_phase(self):
        """誘騙階段: 策略性地污染資源，創造可預測的聚集區域。"""
        induce_phase_start_time = self.env.now
        self.global_logger.log_event(Time_ms=induce_phase_start_time, EventType="RESOURCE_EXHAUSTION_INDUCE_START", UE_ID=self.ue_id,
                                   Details=f"開始誘騙階段，持續時間: {RESOURCE_EXHAUSTION_ATTACK_DURATION_MS}ms，策略：污染部分資源以引導UE行為")
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=induce_phase_start_time, UE_ID=self.ue_id, EventType="INDUCEMENT_START",
                                   Details=f"進入主動誘騙模式，預測目標時槽數: {len(self.predicted_target_resources)}")

        self.attack_phase = "induce"

        # 基於預測選擇要污染的資源
        pollute_resources = list()
        reserved_resources = list()  # 故意保留的乾淨資源

        for slot, subchannels in self.predicted_target_resources.items():
            if len(subchannels) > 1:
                # 如果該時槽有多個乾淨子通道，污染其中一個，保留其他
                chosen_to_pollute = random.choice(subchannels)
                pollute_resources.append((slot, chosen_to_pollute))
                reserved_resources.extend([(slot, sc) for sc in subchannels if sc != chosen_to_pollute])
            else:
                # 如果只有一個乾淨子通道，決定是否污染（70%機率污染）
                if random.random() < 0.7:
                    pollute_resources.append((slot, subchannels[0]))
                else:
                    reserved_resources.append((slot, subchannels[0]))

        # 儲存保留的乾淨資源供伏擊階段使用
        self.current_clean_resources = reserved_resources

        # 執行污染攻擊
        scheduled_attacks = 0
        if pollute_resources:
            for relative_slot, subchannel in pollute_resources:
                # 計算攻擊的絕對時間點
                attack_time = induce_phase_start_time + relative_slot
                # 使用獨立的 process 來處理未來的攻擊事件
                self.env.process(self.disrupt_resource(attack_time, subchannel, self.disruption_power_dbm))
                scheduled_attacks += 1

        self.global_logger.log_event(Time_ms=induce_phase_start_time, EventType="RESOURCE_EXHAUSTION_INDUCE_EXECUTE",
                                      UE_ID=self.ue_id, Details=f"誘騙攻擊部署完成 - 污染資源: {len(pollute_resources)}, 保留資源: {len(reserved_resources)}, 已排程攻擊: {scheduled_attacks}")
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=induce_phase_start_time, UE_ID=self.ue_id, EventType="INDUCEMENT_EXECUTE",
                                     Details=f"誘騙策略執行 - 污染: {len(pollute_resources)}, 保留: {len(reserved_resources)}, 功率: {self.disruption_power_dbm}dBm")

        # 等待誘騙階段完成
        yield self.env.timeout(RESOURCE_EXHAUSTION_ATTACK_DURATION_MS)

        induce_phase_end_time = self.env.now
        actual_duration = induce_phase_end_time - induce_phase_start_time
        self.global_logger.log_event(Time_ms=induce_phase_end_time, EventType="RESOURCE_EXHAUSTION_INDUCE_END", UE_ID=self.ue_id,
                                   Details=f"誘騙階段結束，實際持續時間: {actual_duration}ms")

    def ambush_phase(self):
        """伏擊階段: 基於預測攻擊UE聚集的資源。"""
        ambush_phase_start_time = self.env.now
        self.global_logger.log_event(Time_ms=ambush_phase_start_time, EventType="RESOURCE_EXHAUSTION_AMBUSH_START", UE_ID=self.ue_id,
                                   Details=f"開始伏擊階段，持續時間: {RESOURCE_EXHAUSTION_ATTACK_DURATION_MS}ms，目標：高功率攻擊預測的UE聚集區域")
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=ambush_phase_start_time, UE_ID=self.ue_id, EventType="AMBUSH_START",
                                   Details=f"進入伏擊模式，目標資源數: {len(self.current_clean_resources)}, 攻擊功率: {self.jamming_power_dbm}dBm")

        self.attack_phase = "ambush"

        # 攻擊預測的目標資源（上一輪留下的乾淨資源）
        scheduled_ambush_attacks = 0
        ambush_timeline = []

        if self.current_clean_resources:
            for relative_slot, subchannel in self.current_clean_resources:
                # 計算攻擊的絕對時間點
                attack_time = ambush_phase_start_time + relative_slot
                ambush_timeline.append((attack_time, relative_slot, subchannel))
                # 使用高功率進行伏擊攻擊
                self.env.process(self.disrupt_resource(attack_time, subchannel, self.jamming_power_dbm))
                scheduled_ambush_attacks += 1

        # 計算伏擊覆蓋率
        if len(self.predicted_target_resources) > 0:
            total_predicted_resources = sum(len(subchannels) for subchannels in self.predicted_target_resources.values())
            ambush_coverage = len(self.current_clean_resources) / total_predicted_resources * 100 if total_predicted_resources > 0 else 0
        else:
            ambush_coverage = 0

        self.global_logger.log_event(Time_ms=ambush_phase_start_time, EventType="RESOURCE_EXHAUSTION_AMBUSH_EXECUTE",
                                      UE_ID=self.ue_id, Details=f"伏擊攻擊部署完成 - 目標數: {len(self.current_clean_resources)}, 覆蓋率: {ambush_coverage:.1f}%, 已排程攻擊: {scheduled_ambush_attacks}")
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=ambush_phase_start_time, UE_ID=self.ue_id, EventType="AMBUSH_EXECUTE",
                                     Details=f"伏擊策略執行 - 目標: {len(self.current_clean_resources)}, 功率: {self.jamming_power_dbm}dBm, 覆蓋率: {ambush_coverage:.1f}%")

        # 等待伏擊階段完成
        yield self.env.timeout(RESOURCE_EXHAUSTION_ATTACK_DURATION_MS)

        ambush_phase_end_time = self.env.now
        actual_duration = ambush_phase_end_time - ambush_phase_start_time
        self.global_logger.log_event(Time_ms=ambush_phase_end_time, EventType="RESOURCE_EXHAUSTION_AMBUSH_END", UE_ID=self.ue_id,
                                   Details=f"伏擊階段結束，實際持續時間: {actual_duration}ms，執行攻擊數: {scheduled_ambush_attacks}")

    def disrupt_resource(self, attack_time, subchannel, power_dbm):
        """在指定的時間點對指定的子通道執行干擾。"""
        # 等待直到指定的攻擊時間
        time_to_wait = attack_time - self.env.now
        if time_to_wait > 0:
            yield self.env.timeout(time_to_wait)

        # 執行干擾
        current_time = self.env.now
        resource_to_attack = (current_time, subchannel)

        # 記錄干擾事件
        duration = DISRUPTION_DURATION_MS if power_dbm == self.disruption_power_dbm else ATTACKER_JAMMING_DURATION_MS
        self.resource_pool.log_jamming(self.ue_id, resource_to_attack, power_dbm, duration)

        # 確定攻擊類型和階段
        attack_type = "DISRUPTION" if power_dbm == self.disruption_power_dbm else "JAMMING"
        phase_info = f"階段: {self.attack_phase.upper()}"

        self.global_logger.log_event(Time_ms=current_time, EventType=f"RESOURCE_ATTACK_EXECUTED", UE_ID=self.ue_id,
                                   Details=f"{attack_type}攻擊執行 - 資源: {resource_to_attack}, 功率: {power_dbm}dBm, 持續: {duration}ms, {phase_info}")

        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=current_time, UE_ID=self.ue_id,
                                    EventType=f"RESOURCE_EXHAUSTION_{attack_type}",
                                    Resource_Details=str(resource_to_attack),
                                    Details=f"攻擊執行 - 功率: {power_dbm}dBm, 持續: {duration}ms, 階段: {self.attack_phase}, 延遲: {current_time - (attack_time - time_to_wait) if time_to_wait > 0 else 0}ms")

    def receiver_process(self):
        """持續監聽其他UE的傳輸。只有在 is_listening 為 True 時才記錄數據。"""
        while True:
            # 以較高的頻率輪詢以捕捉所有傳輸
            yield self.env.timeout(RECEIVER_POLLING_INTERVAL_MS)

            if not self.is_listening:
                continue

            current_time = self.env.now
            # 更新自身位置
            current_mobility_state = self.mobility_model.get_ue_state(self.ue_id)
            self.pos = current_mobility_state["pos"]

            active_transmissions_copy = dict(self.resource_pool.active_transmissions)

            for trans_info in active_transmissions_copy.values():
                # 檢查傳輸是否在當前輪詢時間點附近活躍
                if not (trans_info["start_time"] <= current_time < trans_info["end_time"]):
                    continue

                sender_id = trans_info["ue_id"]
                if sender_id == self.ue_id:
                    continue

                resource = trans_info["resource"]
                tx_power_dbm = trans_info["tx_power"]

                sender_ue_object = self.all_other_ues.get(sender_id)
                if not sender_ue_object:
                    continue

                distance = self.comm_model.calculate_distance(self.pos, sender_ue_object.pos)
                if distance > MAX_COMM_RANGE_M:
                    continue

                received_power_dbm = self.comm_model.calculate_received_power_dbm(tx_power_dbm, distance)
                if received_power_dbm < MIN_RX_POWER_DBM:
                    continue

                # 記錄感知到的資源使用情況
                if resource not in self.sensed_resource_rsrp:
                    self.sensed_resource_rsrp[resource] = received_power_dbm
