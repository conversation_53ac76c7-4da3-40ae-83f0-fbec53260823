"""
資源池擾亂攻擊 (Resource Pool Disruption Attack)

此攻擊方法試圖擾亂整個通信資源池的使用效率，迫使合法車輛在不利的通信條件下運行。
攻擊者通過佔用相對「乾淨」(干擾較小) 的通信資源，迫使其他車輛選擇較為「嘈雜」(干擾較大) 的資源，
增加訊息碰撞和接收失敗的概率。

攻擊步驟：
1. 監聽階段：觀察資源使用情況，確定哪些是相對空閒(乾淨)的資源
2. 攻擊階段：在識別出的相對乾淨的資源上發送一階SCI訊息，佔用這些資源
"""

import random
from src.CONFIG import *
from src.ue import VehicleUE, DEFAULT_PRSRP
from src.mobility import MODEL_LINEAR
from src.logger import Logger # Assuming Logger class is in logger.py
from src.simulators.simulator import NRV2XSimulator
from src.attacker_model.resource_pool_disruptor import ResourcePoolDisruptorUE


class Simulator4(NRV2XSimulator):
        def __init__(self, simulation_index, normal_ue_count=5, attacker_ue_count=1):
            super().__init__(simulation_index)
            self.normal_ue_count = normal_ue_count
            self.attacker_ue_count = attacker_ue_count

        def setup_scenario(self):
            self.global_logger.log_event(Time_ms=self.env.now, EventType="SCENARIO_SETUP_START", UE_ID="System")
            print(f"Time {self.env.now}: Setting up scenario...")

            # 創建正常車輛
            for i in range(self.normal_ue_count):
                ue_id = f"NormalUE-{i}"
                initial_pos = (random.uniform(0, AREA_WIDTH), random.uniform(0, AREA_HEIGHT))
                initial_dir_deg = 90
                initial_speed_mps = random.uniform(8, 15)
                
                # 創建UE特定日誌
                ue_logger_instance = Logger(filename=f"{ue_id}.csv", log_dir=self.ue_log_dir, fieldnames=UE_SPECIFIC_LOG_FIELDNAMES)
                self.ue_loggers.append(ue_logger_instance)
                
                ue = VehicleUE(self.env, ue_id, self.resource_pool, self.comm_model, self.global_logger, self.mobility_model,
                               initial_pos=initial_pos, initial_speed=initial_speed_mps,
                               prsvp=PRSVP, ue_specific_logger=ue_logger_instance)
                self.normal_ues.append(ue)
                self.all_ues.append(ue)
                self.ues_objects_dict[ue_id] = ue
                self.mobility_model.initialize_ue_state(ue_id, model_type=MODEL_LINEAR, 
                                                       initial_pos=initial_pos, 
                                                       initial_speed_mps=initial_speed_mps, 
                                                       initial_direction_deg=initial_dir_deg)
                self.global_logger.log_event(Time_ms=self.env.now, EventType="UE_CREATED", UE_ID=ue_id, Details="Type: Normal")
                print(f"  {ue_id} created.")

            # 創建資源池擾亂攻擊者
            for i in range(self.attacker_ue_count):
                attacker_id = f"ResourceDisruptorUE-{i}"
                initial_pos = (random.uniform(0, AREA_WIDTH), random.uniform(0, AREA_HEIGHT))
                initial_dir_deg = random.uniform(0, 360)
                initial_speed_mps = random.uniform(10, 12)
                
                # 創建攻擊者特定日誌
                
                attacker_logger_instance = Logger(filename=f"{attacker_id}.csv", log_dir=self.ue_log_dir, fieldnames=UE_SPECIFIC_LOG_FIELDNAMES)
                self.ue_loggers.append(attacker_logger_instance)
                
                # 使用ResourcePoolDisruptorUE類別
                attacker = ResourcePoolDisruptorUE(self.env, attacker_id, self.resource_pool, self.comm_model, 
                                                 self.global_logger, self.mobility_model,
                                                 initial_pos=initial_pos, initial_speed=initial_speed_mps,
                                                 ue_specific_logger=attacker_logger_instance)
                self.attacker_ues.append(attacker)
                self.all_ues.append(attacker)
                self.ues_objects_dict[attacker_id] = attacker
                self.mobility_model.initialize_ue_state(attacker_id, model_type=MODEL_LINEAR, 
                                                         initial_pos=initial_pos, 
                                                         initial_speed_mps=initial_speed_mps, 
                                                         initial_direction_deg=initial_dir_deg)
                self.global_logger.log_event(Time_ms=self.env.now, EventType="UE_CREATED", UE_ID=attacker_id, Details=f"Type: ResourceDisruptor")
                print(f"  {attacker_id} created.")

            for ue in self.all_ues:
                ue.all_other_ues = {other_ue.ue_id: other_ue for other_ue in self.all_ues if other_ue.ue_id != ue.ue_id}
                ue.comm_model = self.comm_model 
                ue.resource_pool = self.resource_pool

            self.global_logger.log_event(Time_ms=self.env.now, EventType="SCENARIO_SETUP_END", UE_ID="System")
            print(f"Time {self.env.now}: Scenario setup complete. Total UEs: {len(self.all_ues)}")
    