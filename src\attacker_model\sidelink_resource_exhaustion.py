
"""
側鏈資源耗盡攻擊 (Sidelink Resource Exhaustion Attack) - 誘騙與伏擊實現

此攻擊模型實現了一個智能的兩階段攻擊策略：
1. 誘騙階段 (Inducement Phase): 策略性地污染大部分資源，故意留下少數特定的"乾淨"資源，
   驅趕正常UE選擇這些特定資源。
2. 伏擊階段 (Ambush Phase): 基於學習到的UE行為模式，預測下一輪UE會聚集的資源區域，
   並針對性地進行高功率干擾攻擊。

攻擊邏輯：
1. **監聽與學習週期 (100ms)**: 攻擊者進入純監聽模式，收集UE行為數據，
   分析資源使用模式，建立預測模型。
2. **誘騙階段**: 基於分析結果，策略性地污染大部分資源，創造可預測的資源聚集區域。
3. **伏擊階段**: 根據學習到的模式，預測UE會選擇的資源並進行精準攻擊。
4. **適應性學習**: 持續更新預測模型，提高攻擊效果。
"""

import random
import simpy
from collections import defaultdict, deque
from src.CONFIG import *

class SidelinkResourceExhaustionUE:
    def __init__(self, env, ue_id, resource_pool, comm_model, global_logger, mobility_model,
                 initial_pos=(0,0), initial_speed=0,
                 jamming_power_dbm=ATTACKER_JAMMING_POWER_DBM,
                 disruption_power_dbm=DISRUPTION_POWER_DBM,
                 ue_specific_logger=None):
        # 基本UE屬性初始化
        self.env = env
        self.ue_id = ue_id
        self.resource_pool = resource_pool
        self.comm_model = comm_model
        self.global_logger = global_logger
        self.ue_logger = ue_specific_logger
        self.mobility_model = mobility_model

        self.pos = initial_pos
        self.speed = initial_speed

        self.is_attacker = True
        self.all_other_ues = {}

        # 攻擊者特定參數
        self.jamming_power_dbm = jamming_power_dbm
        self.disruption_power_dbm = disruption_power_dbm

        # 攻擊狀態管理
        self.attack_phase = "listen"  # listen, induce, ambush
        self.is_listening = False

        # 學習與預測機制
        self.ue_behavior_history = defaultdict(lambda: deque(maxlen=PREDICTION_HISTORY_SIZE))
        self.resource_usage_patterns = defaultdict(int)
        self.predicted_target_resources = {}
        self.current_clean_resources = []

        # 監聽數據收集
        self.sensed_resource_rsrp = {}
        self.cycle_start_time = 0

        self.global_logger.log_event(Time_ms=self.env.now, EventType="RESOURCE_EXHAUSTION_INIT", UE_ID=self.ue_id,
                              Details=f"Jamming Power: {self.jamming_power_dbm}dBm, Disruption Power: {self.disruption_power_dbm}dBm")

        # 啟動攻擊者的運行邏輯和接收器進程
        self.action = env.process(self.attacker_run_logic())
        self.receiver_process_action = env.process(self.receiver_process())

    def attacker_run_logic(self):
        """攻擊者的主要運行邏輯，實現誘騙與伏擊循環。"""
        self.global_logger.log_event(Time_ms=self.env.now, EventType="RESOURCE_EXHAUSTION_LOGIC_START", UE_ID=self.ue_id)

        while True:
            # 1. 監聽與學習階段
            yield self.env.process(self.listen_and_learn_phase())
            # 2. 誘騙階段
            yield self.env.process(self.inducement_phase())
            # 3. 伏擊階段
            yield self.env.process(self.ambush_phase())

    def listen_and_learn_phase(self):
        """監聽與學習階段: 收集UE行為數據，更新預測模型。"""
        listen_phase_start_time = self.env.now
        self.cycle_start_time = listen_phase_start_time
        self.global_logger.log_event(Time_ms=listen_phase_start_time, EventType="RESOURCE_EXHAUSTION_LISTEN_START", UE_ID=self.ue_id)
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=listen_phase_start_time, UE_ID=self.ue_id, EventType="LISTEN_LEARN_START")

        # 清空先前的感知數據並開始監聽
        self.sensed_resource_rsrp.clear()
        self.is_listening = True
        self.attack_phase = "listen"

        # 監聽指定時間
        yield self.env.timeout(RESOURCE_EXHAUSTION_LISTEN_DURATION_MS)

        # 停止監聽並開始分析
        self.is_listening = False
        listen_phase_end_time = self.env.now

        # 分析收集到的數據並更新學習模型
        self._analyze_and_learn()

        self.global_logger.log_event(Time_ms=listen_phase_end_time, EventType="RESOURCE_EXHAUSTION_LISTEN_END",
                                      UE_ID=self.ue_id, Details=f"Learned from {len(self.sensed_resource_rsrp)} observations")
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=listen_phase_end_time, UE_ID=self.ue_id, EventType="LISTEN_LEARN_END",
                                     Details=f"Observations: {len(self.sensed_resource_rsrp)}")

    def _analyze_and_learn(self):
        """分析監聽數據，更新UE行為預測模型。"""
        current_time = self.env.now

        # 分析資源使用模式
        used_resources_relative = set()
        for (absolute_slot, subchannel), rsrp in self.sensed_resource_rsrp.items():
            relative_slot = absolute_slot % 100
            used_resources_relative.add((relative_slot, subchannel))
            # 更新資源使用模式統計
            self.resource_usage_patterns[(relative_slot, subchannel)] += 1

        # 識別當前週期的乾淨資源
        all_resources = [(slot, subchannel) for slot in range(100) for subchannel in range(NUM_SUBCHANNELS)]
        current_clean_resources = [res for res in all_resources if res not in used_resources_relative]
        # 更新UE行為歷史（簡化版本，實際可以更複雜）
        self.ue_behavior_history[current_time].append({
            'used_resources': used_resources_relative,
            'clean_resources': current_clean_resources,
            'total_activity': len(used_resources_relative)
        })

        # 基於歷史數據預測下一輪UE可能選擇的資源
        self._predict_target_resources(current_clean_resources)

        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=current_time, UE_ID=self.ue_id, EventType="LEARNING_UPDATE",
                                     Details=f"Clean resources: {len(current_clean_resources)}, Used: {len(used_resources_relative)}")

    def _predict_target_resources(self, current_clean_resources):
        """基於學習到的模式預測UE下一輪可能選擇的資源。"""
        # # 簡化的預測算法：基於歷史使用頻率和當前乾淨資源
        # resource_scores = defaultdict(float)

        # # 計算每個資源的預測分數
        # for res, usage_count in self.resource_usage_patterns.items():
        #     # 基礎分數：歷史使用頻率（越少使用的資源越可能被選中）
        #     base_score = 1.0 / (usage_count + 1)
        #     resource_scores[res] = base_score

        # # 選擇分數最高的資源作為預測目標
        # sorted_resources = sorted(resource_scores.items(), key=lambda x: x[1], reverse=True)

        # # 選擇前20%作為預測目標
        # num_targets = max(1, int(len(sorted_resources) * FUNNEL_RATIO))
        # self.predicted_target_resources = [res for res, score in sorted_resources[:num_targets]]
        current_clean_resources_dict = {}
        for res in current_clean_resources:
            if res[0] not in current_clean_resources_dict:
                current_clean_resources_dict[res[0]] = []
            current_clean_resources_dict[res[0]].append(res[1])

        self.predicted_target_resources = current_clean_resources_dict

        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=self.env.now, UE_ID=self.ue_id, EventType="PREDICTION_UPDATE",
                                     Details=f"Predicted {len(self.predicted_target_resources)} target resources")

    def inducement_phase(self):
        """誘騙階段: 策略性地污染資源，創造可預測的聚集區域。"""
        induce_phase_start_time = self.env.now
        self.global_logger.log_event(Time_ms=induce_phase_start_time, EventType="RESOURCE_EXHAUSTION_INDUCE_START", UE_ID=self.ue_id)
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=induce_phase_start_time, UE_ID=self.ue_id, EventType="INDUCEMENT_START")

        self.attack_phase = "induce"

        # 獲取當前可用資源
        current_time = self.env.now
        sensing_window_start_time = current_time - DEFAULT_SENSING_WINDOW_T0

        # 模擬感知過程（簡化版本）
        # all_resources = self.resource_pool.active_transmissions

        # # 基於預測選擇要保留的乾淨資源
        # # if self.predicted_target_resources:
        # #     clean_resources = self.predicted_target_resources.copy()
        # # else:
        # #     # 如果沒有預測數據，隨機選擇
        # #     num_clean = max(1, int(len(all_resources) * FUNNEL_RATIO))
        # #     clean_resources = random.sample(all_resources, num_clean)
        # clean_resources = self.predicted_target_resources

        # self.current_clean_resources = clean_resources

        # # 選擇要污染的資源（除了乾淨資源外的其他資源）
        # pollute_resources = [res for res in all_resources if res not in clean_resources]
        pollute_resources = list()
        for slot, subchannels in self.predicted_target_resources.items():
            pollute_resources.append((slot, random.choice(subchannels)))

        # 執行污染攻擊
        if pollute_resources:
            for relative_slot, subchannel in pollute_resources:
                # 計算攻擊的絕對時間點
                attack_time = induce_phase_start_time + relative_slot
                # 使用獨立的 process 來處理未來的攻擊事件
                self.env.process(self.disrupt_resource(attack_time, subchannel, self.disruption_power_dbm))

        self.global_logger.log_event(Time_ms=current_time, EventType="RESOURCE_EXHAUSTION_INDUCE_EXECUTE",
                                      UE_ID=self.ue_id, Details=f"Polluted {len(pollute_resources)} resources")
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=current_time, UE_ID=self.ue_id, EventType="INDUCEMENT_EXECUTE",
                                     Details=f"Pollute: {len(pollute_resources)}")

        # 等待誘騙階段完成
        yield self.env.timeout(RESOURCE_EXHAUSTION_ATTACK_DURATION_MS)
        self.global_logger.log_event(Time_ms=self.env.now, EventType="RESOURCE_EXHAUSTION_INDUCE_END", UE_ID=self.ue_id)

    def ambush_phase(self):
        """伏擊階段: 基於預測攻擊UE聚集的資源。"""
        ambush_phase_start_time = self.env.now
        self.global_logger.log_event(Time_ms=ambush_phase_start_time, EventType="RESOURCE_EXHAUSTION_AMBUSH_START", UE_ID=self.ue_id)
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=ambush_phase_start_time, UE_ID=self.ue_id, EventType="AMBUSH_START")

        self.attack_phase = "ambush"

        # 攻擊預測的目標資源（上一輪留下的乾淨資源）
        if self.current_clean_resources:
            for relative_slot, subchannel in self.current_clean_resources:
                # 計算攻擊的絕對時間點
                attack_time = ambush_phase_start_time + relative_slot
                # 使用高功率進行伏擊攻擊
                self.env.process(self.disrupt_resource(attack_time, subchannel, self.jamming_power_dbm))

        self.global_logger.log_event(Time_ms=self.env.now, EventType="RESOURCE_EXHAUSTION_AMBUSH_EXECUTE",
                                      UE_ID=self.ue_id, Details=f"Ambushing {len(self.current_clean_resources)} predicted targets")
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=self.env.now, UE_ID=self.ue_id, EventType="AMBUSH_EXECUTE",
                                     Details=f"Targets: {len(self.current_clean_resources)}")

        # 等待伏擊階段完成
        yield self.env.timeout(RESOURCE_EXHAUSTION_ATTACK_DURATION_MS)
        self.global_logger.log_event(Time_ms=self.env.now, EventType="RESOURCE_EXHAUSTION_AMBUSH_END", UE_ID=self.ue_id)

    def disrupt_resource(self, attack_time, subchannel, power_dbm):
        """在指定的時間點對指定的子通道執行干擾。"""
        # 等待直到指定的攻擊時間
        time_to_wait = attack_time - self.env.now
        if time_to_wait > 0:
            yield self.env.timeout(time_to_wait)

        # 執行干擾
        current_time = self.env.now
        resource_to_attack = (current_time, subchannel)

        # 記錄干擾事件
        duration = DISRUPTION_DURATION_MS if power_dbm == self.disruption_power_dbm else ATTACKER_JAMMING_DURATION_MS
        self.resource_pool.log_jamming(self.ue_id, resource_to_attack, power_dbm, duration)

        if self.ue_logger:
            attack_type = "DISRUPTION" if power_dbm == self.disruption_power_dbm else "JAMMING"
            self.ue_logger.log_event(Time_ms=current_time, UE_ID=self.ue_id,
                                    EventType=f"RESOURCE_EXHAUSTION_{attack_type}",
                                    Resource_Details=str(resource_to_attack),
                                    Details=f"Power: {power_dbm}dBm")

    def receiver_process(self):
        """持續監聽其他UE的傳輸。只有在 is_listening 為 True 時才記錄數據。"""
        while True:
            # 以較高的頻率輪詢以捕捉所有傳輸
            yield self.env.timeout(RECEIVER_POLLING_INTERVAL_MS)

            if not self.is_listening:
                continue

            current_time = self.env.now
            # 更新自身位置
            current_mobility_state = self.mobility_model.get_ue_state(self.ue_id)
            self.pos = current_mobility_state["pos"]

            active_transmissions_copy = dict(self.resource_pool.active_transmissions)

            for trans_info in active_transmissions_copy.values():
                # 檢查傳輸是否在當前輪詢時間點附近活躍
                if not (trans_info["start_time"] <= current_time < trans_info["end_time"]):
                    continue

                sender_id = trans_info["ue_id"]
                if sender_id == self.ue_id:
                    continue

                resource = trans_info["resource"]
                tx_power_dbm = trans_info["tx_power"]

                sender_ue_object = self.all_other_ues.get(sender_id)
                if not sender_ue_object:
                    continue

                distance = self.comm_model.calculate_distance(self.pos, sender_ue_object.pos)
                if distance > MAX_COMM_RANGE_M:
                    continue

                received_power_dbm = self.comm_model.calculate_received_power_dbm(tx_power_dbm, distance)
                if received_power_dbm < MIN_RX_POWER_DBM:
                    continue

                # 記錄感知到的資源使用情況
                if resource not in self.sensed_resource_rsrp:
                    self.sensed_resource_rsrp[resource] = received_power_dbm
