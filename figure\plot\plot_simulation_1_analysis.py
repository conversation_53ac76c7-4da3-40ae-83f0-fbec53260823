import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os

# --- Configuration ---
TIME_WINDOW = 100  # ms
FIGURE_FILENAME = "simulation_1_pdr_analysis.png"

# --- File Paths ---
script_dir = os.path.dirname(os.path.abspath(__file__))
base_path = os.path.join(script_dir, "..", "..", "output", "nr_v2x_simulator_simulation_results", "simulation_1")
output_path = os.path.join(script_dir, FIGURE_FILENAME)

# --- Load Data ---
try:
    attacker_df = pd.read_csv(os.path.join(base_path, "AttackerUE-0.csv"))
    victim_df = pd.read_csv(os.path.join(base_path, "NormalUE-0.csv"))
    normal1_df = pd.read_csv(os.path.join(base_path, "NormalUE-1.csv"))
    normal2_df = pd.read_csv(os.path.join(base_path, "NormalUE-2.csv"))
except FileNotFoundError as e:
    print(f"Error loading file: {e}. Please ensure the CSV files are in the correct directory.")
    exit()

# --- PDR Calculation Function ---
def calculate_pdr(tx_df, rx_df, sender_id, time_window=100):
    tx_events = tx_df[tx_df['EventType'] == 'PSSCH_TX']
    
    pdr_data = []
    max_time = max(tx_df['Time_ms'].max(), rx_df['Time_ms'].max())
    time_bins = np.arange(0, max_time + time_window, time_window)

    for i in range(len(time_bins) - 1):
        start_time, end_time = time_bins[i], time_bins[i+1]
        
        window_tx_count = tx_events[
            (tx_events['Time_ms'] >= start_time) & (tx_events['Time_ms'] < end_time)
        ].shape[0]
        
        if window_tx_count == 0:
            continue

        rx_events_successful = rx_df[
            (rx_df['Related_UE_ID'] == sender_id) &
            (rx_df['EventType'] == 'PSSCH_RX_OUTCOME') &
            (rx_df['Outcome'] == 'SUCCESS') &
            (rx_df['Time_ms'] >= start_time) & 
            (rx_df['Time_ms'] < end_time + 10) # Allow for small delay
        ].shape[0]

        pdr = rx_events_successful / window_tx_count if window_tx_count > 0 else 0
        pdr_data.append({'Time': (start_time + end_time) / 2, 'PDR': pdr})
        
    return pd.DataFrame(pdr_data)

# --- Calculate PDR for relevant pairs ---
pdr_victim_to_normal1 = calculate_pdr(victim_df, normal1_df, 'NormalUE-0')
pdr_victim_to_normal2 = calculate_pdr(victim_df, normal2_df, 'NormalUE-0')
pdr_normal1_to_normal2 = calculate_pdr(normal1_df, normal2_df, 'NormalUE-1')
pdr_normal2_to_normal1 = calculate_pdr(normal2_df, normal1_df, 'NormalUE-2')

# --- Create Plot ---
plt.style.use('seaborn-v0_8-whitegrid')
fig, ax = plt.subplots(figsize=(16, 8))

# Plot Victim's PDR
ax.plot(pdr_victim_to_normal1['Time'], pdr_victim_to_normal1['PDR'], marker='o', linestyle='-', label='PDR: Victim (UE-0) -> NormalUE-1', color='red', zorder=10)
ax.plot(pdr_victim_to_normal2['Time'], pdr_victim_to_normal2['PDR'], marker='x', linestyle='-', label='PDR: Victim (UE-0) -> NormalUE-2', color='darkred', zorder=10)

# Plot Control PDRs
ax.plot(pdr_normal1_to_normal2['Time'], pdr_normal1_to_normal2['PDR'], marker='.', linestyle='--', label='PDR: NormalUE-1 -> NormalUE-2 (Control)', color='blue', alpha=0.7)
ax.plot(pdr_normal2_to_normal1['Time'], pdr_normal2_to_normal1['PDR'], marker='.', linestyle='--', label='PDR: NormalUE-2 -> NormalUE-1 (Control)', color='green', alpha=0.7)

# Highlight Attack Periods
attacker_jam_times = attacker_df[attacker_df['EventType'] == 'ATTACKER_JAM_EXECUTE']['Time_ms']
for jam_time in attacker_jam_times:
    ax.axvline(x=jam_time, color='grey', linestyle=':', linewidth=1.5, alpha=0.8, label='Attacker Jamming' if jam_time == attacker_jam_times.iloc[0] else "")

# --- Final Touches ---
ax.set_title('Packet Delivery Ratio (PDR) Analysis under Jamming Attack', fontsize=16)
ax.set_xlabel('Time (ms)', fontsize=12)
ax.set_ylabel('PDR', fontsize=12)
ax.set_ylim(-0.1, 1.1)
ax.legend(loc='best')
ax.grid(True, which='both', linestyle='--', linewidth=0.5)

plt.tight_layout()
# Ensure the output directory exists
output_dir = os.path.dirname(output_path)
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
plt.savefig(output_path)
print(f"Analysis plot saved to {output_path}")