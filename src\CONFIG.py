import random

#========================simulation_config.py========================
PRSVP = 100
SIM_DURATION_MS = 5000  # ms
NUM_NORMAL_UES = 5
NUM_ATTACKER_UES = 1
ATTACKER_VICTIM_ID = "NormalUE-0" # Specific victim for the attacker, if any

# Area dimensions for mobility (meters)
AREA_WIDTH = 1000
AREA_HEIGHT = 100

# Mobility update interval (ms)
MOBILITY_UPDATE_INTERVAL_MS = 100

# Logging configuration
LOG_DIR = "logs"
LOG_FILENAME = "simulation_run.csv"

#========================mobility.py========================
# Mobility Model Types (examples)
MODEL_STATIC = 0
MODEL_LINEAR = 1 # Constant speed, straight line
MODEL_RANDOM_WAYPOINT = 2 # Not fully implemented here, placeholder

#========================ue.py========================
# Default SPS Parameters (can be overridden per UE)
DEFAULT_PRSRP = 100  # ms, Periodicity of reservation
DEFAULT_SLRRC_MAX = 15 # Sidelink Resource Reservation Counter max value (used by UE and as basis for attacker)
DEFAULT_SLRRC_MIN = 5
DEFAULT_NMAX_RESERVE = 2 # Max number of reservations a UE can make
DEFAULT_RESOURCE_KEEP_PROBABILITY = 0.8 # Probability to keep the resource if counter > 0
DEFAULT_SENSING_WINDOW_T0 = 100 # ms
DEFAULT_SELECTION_WINDOW_T1 = 5 # ms (relative to end of T0)
DEFAULT_SELECTION_WINDOW_T2 = 25 # ms (relative to end of T0)
DEFAULT_X_PERCENT_CANDIDATES_THRESHOLD = 0.2 # If available resources in selection window are below this percentage of total, adjust RSRP (Requirement 2)
DEFAULT_X_PERCENT_CANDIDATES_FOR_POOL = 0.2 # Percentage of sensed resources to consider as candidates from the pool (original DEFAULT_X_PERCENT_CANDIDATES)
DEFAULT_NPSSCH_MAX_TX = 1 # Max PSSCH transmissions per UE in a selection window (original, will be overridden by NUM_PSSCH_PER_SCI for multi-PSSCH)
RECEIVER_POLLING_INTERVAL_MS = 1 # How often receiver checks for signals
BSM_GENERATION_INTERVAL_MS = 100 # ms, BSM generation rate (e.g. 10 Hz)

# New configurations based on user request
DEFAULT_SENSING_RSRP_THRESHOLD_DBM = -90.0 # Default RSRP threshold for sensing other UEs' transmissions
NUM_PSSCH_PER_SCI = 2 # Number of PSSCH transmissions announced by a single SCI (e.g., 1 or 3)

# Attacker Specific Configurations
ATTACKER_JAMMING_POWER_DBM = 30.0  # dBm, power of the jamming signal
ATTACKER_JAMMING_DURATION_MS = 2.0 # ms, duration of a single jamming burst (e.g., to cover PSSCH)
ATTACKER_MAX_TRANSMISSIONS_ON_RESOURCE = DEFAULT_SLRRC_MAX # Attacker counts victim's transmissions up to this limit on a resource

# Resource Exhaustion Attacker Configurations
DISRUPTION_POWER_DBM = 15.0  # dBm, lower power for inducement phase pollution
DISRUPTION_DURATION_MS = 1.0 # ms, duration of disruption signals
RESOURCE_EXHAUSTION_LISTEN_DURATION_MS = 100  # ms, listening phase duration
RESOURCE_EXHAUSTION_ATTACK_DURATION_MS = 100  # ms, attack phase duration
FUNNEL_RATIO = 0.2  # Ratio of resources to keep clean for funneling
PREDICTION_HISTORY_SIZE = 5  # Number of previous cycles to consider for prediction
LEARNING_WEIGHT_DECAY = 0.8  # Weight decay for historical data in learning

EMERGENCY_BSM_PROBABILITY = 1
EMERGENCY_BSM_DURATION_MAX = 5
EMERGENCY_BSM_DURATION_MIN = 1

#========================resource_pool.py========================
# Configuration for Resource Pool
NUM_SUBCHANNELS = 5
SLOT_DURATION_MS = 1 
MAX_COMM_RANGE_M = 2000 # Max distance for considering interference or reception
MIN_RX_POWER_DBM = -120 # Min power for a signal to be processed by receiver

# Constants from ue.py (or a shared config) needed for some defaults if not passed explicitly
# These are used in get_candidate_resources if not provided by UE call
DEFAULT_SELECTION_WINDOW_T1_SLOTS = DEFAULT_SELECTION_WINDOW_T1 // SLOT_DURATION_MS 
DEFAULT_SELECTION_WINDOW_T2_SLOTS = DEFAULT_SELECTION_WINDOW_T2 // SLOT_DURATION_MS 

# Configuration for Resource Pool Cleanup (Requirement 4 - ON HOLD)
# RP_CLEANUP_INTERVAL_MS = 1000 
# RP_MAX_HISTORY_SLOTS = 1000 

#=========================communication.py========================
# --- Constants for Communication Model ---
# Path Loss Model: Free Space Path Loss (FSPL) initially
# FSPL (dB) = 20 * log10(d) + 20 * log10(f) + 20 * log10(4*pi/c) - G_tx - G_rx
# For simplicity, we can use a simplified FSPL or a more abstract distance-based model first.
# Let f = 5.9 GHz (common for V2X)
c = 299792458  # Speed of light in m/s
FREQ_V2X_HZ = 5.9e9  # 5.9 GHz

# Path loss constant for FSPL at 5.9 GHz, assuming G_tx = G_rx = 0 dBi for simplicity
# 20*log10(5.9e9) + 20*log10(4*pi/c) = 20*9.77 + 20*(-7.37) = 195.4 - 147.4 = 48 dB (approx)
# More accurately: 20 * math.log10(FREQ_V2X_HZ) + 20 * math.log10(4 * math.pi / c) = 47.88 dB
# So, FSPL(dB) = 47.88 + 20 * log10(d_meters)
PATH_LOSS_CONSTANT = 47.88 # For distance in meters, frequency 5.9GHz
PATH_LOSS_EXPONENT = 2.0   # For Free Space. Can be increased for urban environments (e.g., 2.7 to 3.5)

# Noise Figure of the receiver (dB)
NOISE_FIGURE_DB = 7.0
# Thermal Noise Power Density (dBm/Hz)
# N0 = k * T, where k = 1.380649e-23 J/K (Boltzmann constant), T = 290 K (reference temperature)
# N0_watts_hz = 1.380649e-23 * 290 = 4.00388e-21 W/Hz
# N0_dbm_hz = 10 * log10(N0_watts_hz * 1000) = -173.97 dBm/Hz (approx -174 dBm/Hz)
THERMAL_NOISE_DENSITY_DBM_HZ = -174.0

# Bandwidth for sidelink channels (e.g., 10 MHz or 20 MHz for NR V2X)
# Let's assume an effective bandwidth for SINR calculation for a sub-channel or resource block.
# This needs to be aligned with resource pool definition.
# For now, let's assume a reference bandwidth for noise calculation.
REFERENCE_BANDWIDTH_HZ = 180e3  # Per Resource Block (RB) in LTE/NR (180 kHz)
                               # If a subchannel in resource_pool.py is one RB. Adjust if different.

# Default transmission powers (can be configured per UE or per transmission type)
DEFAULT_TX_POWER_SCI_DBM = 10.0  # dBm
DEFAULT_TX_POWER_PSSCH_DBM = 23.0 # dBm

# SINR thresholds for decoding (placeholders, these are highly dependent on MCS, coding rate, etc.)
SINR_THRESHOLD_SCI_DB = 3.0  # dB
SINR_THRESHOLD_PSSCH_DB = 5.0 # dB (this would vary significantly with MCS)

# Transmission times (placeholders, can be more dynamic)
SCI_TX_TIME_MS = 1 # ms (typically 1 slot or less)
PSSCH_BASE_TX_TIME_MS = 1 # ms (minimum, e.g., for one slot)
PSSCH_TIME_PER_BYTE_MS = 0.01 # ms, very rough estimate for data rate impact


UE_SPECIFIC_LOG_FIELDNAMES = [
    "Time_ms", "EventType", "Resource_Details","SLRRC", "SCI_Content_Abstract", 
    "PSSCH_Content_Abstract", "RSRP_dBm", "SINR_dB", "Is_Selected_By_Self", 
    "Is_Transmission", "Related_UE_ID", "Outcome", "Raw_Candidate_Resources", "Details"
]

#========================simulator_4.py========================
SIM4_NUM_NORMAL_UES = 10
SIM4_NUM_ATTACKER_UES = 1
