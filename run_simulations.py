
"""
主模擬運行腳本 - 整合多種攻擊方法

此腳本用於運行多種攻擊方法的模擬並收集結果：
1. 針對性一階SCI干擾攻擊 (Targeted 1st-stage SCI Jamming)
2. 碰撞關鍵訊息攻擊 (Collision of Critical Message Attack)
3. 資源池擾亂攻擊 (Resource Pool Disruption Attack)
4. 旁路資源耗盡攻擊 (Sidelink Resource Exhaustion Attack)
"""

import os
import shutil

# 導入模擬器核心模組
from src.simulators.simulator import NRV2XSimulator
from src.simulators.simulator_2 import Simulator2
from src.simulators.simulator_3 import Simulator3
from src.simulators.simulator_4 import Simulator4 # Import new simulator
from src.simulators.simulator_2_half_duplex import Simulator2HalfDuplex

from src.CONFIG import *

# 導入攻擊方
from src.mobility import MODEL_LINEAR

def setup_output_dirs():
    """設置輸出目錄"""
    base_dir = 'output/nr_v2x_simulator_simulation_results'
    
    if not os.path.exists(base_dir):
        os.makedirs(base_dir)
    
    attack_dirs = {
        'simulation_1': os.path.join(base_dir, 'simulation_1'),
        'simulation_2': os.path.join(base_dir, 'simulation_2'),
        'simulation_3': os.path.join(base_dir, 'simulation_3'),
        'simulation_4': os.path.join(base_dir, 'simulation_4'), # Add dir for sim 4
        'simulation_2_half_duplex': os.path.join(base_dir, 'simulation_2_half_duplex')
    }
    
    for dir_path in attack_dirs.values():
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
    
    return attack_dirs

def run_simulation_1():
    """運行針對性一階SCI干擾攻擊模擬"""
    print("\n=== 運行模擬 1: 針對性一階SCI干擾攻擊 ===")
    simulator = NRV2XSimulator(simulation_index='simulation_1', num_normal_ues=NUM_NORMAL_UES, num_attacker_ues=NUM_ATTACKER_UES, attacker_victim_id=ATTACKER_VICTIM_ID)
    simulator.run_simulation()
    return simulator.ue_log_dir

def run_simulation_2():
    """運行碰撞關鍵訊息攻擊模擬"""
    print("\n=== 運行模擬 2: 碰撞關鍵訊息攻擊 ===")
    simulator = Simulator2(simulation_index="simulation_2")
    simulator.run_simulation()
    return simulator.ue_log_dir

def run_simulation_2_half_duplex():
    """運行碰撞關鍵訊息攻擊模擬 (半雙工模式)"""
    print("\n=== 運行模擬 2 (半雙工): 碰撞關鍵訊息攻擊 ===")
    simulator = Simulator2HalfDuplex(simulation_index="simulation_2_half_duplex")
    simulator.run_simulation()
    return simulator.ue_log_dir

def run_simulation_3():
    """運行資源池擾亂攻擊模擬"""
    print("\n=== 運行模擬 3: 資源池擾亂攻擊 ===")
    simulator = Simulator3(simulation_index="simulation_3", normal_ue_count= 10)
    simulator.run_simulation()
    return simulator.ue_log_dir

def run_simulation_4():
    """運行旁路資源耗盡攻擊模擬"""
    print("\n=== 運行模擬 4: 旁路資源耗盡攻擊 ===")
    simulator = Simulator4(simulation_index="simulation_4", normal_ue_count= 10)
    simulator.run_simulation()
    print("模擬 4 完成")
    return simulator.ue_log_dir

def copy_logs(source_dir, dest_dir):
    """複製日誌文件到結果目錄"""
    if os.path.exists(source_dir):
        for file_name in os.listdir(source_dir):
            source_file = os.path.join(source_dir, file_name)
            dest_file = os.path.join(dest_dir, file_name)
            shutil.copy2(source_file, dest_file)
        print(f"日誌已複製到 {dest_dir}")
    else:
        print(f"警告: 源目錄 {source_dir} 不存在")

def analyze_results(attack_dirs):
    """分析模擬結果並生成報告"""
    report_path = os.path.join('output/nr_v2x_simulator_simulation_results', 'simulation_report.md')
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("# NR-V2X 攻擊方法模擬報告\n\n")
        
        # Simulation 1
        f.write("## 模擬 1: 針對性一階 SCI 干擾攻擊\n\n")
        f.write("### 攻擊目標與原理\n")
        f.write("選擇性地、精確地阻止特定目標車輛的一階 SCI 被其周圍車輛成功接收和解碼，使目標車輛在車聯網中變為「不可見」的「幽靈車輛」。\n\n")
        
        # Simulation 2
        f.write("## 模擬 2: 碰撞關鍵訊息攻擊\n\n")
        f.write("### 攻擊目標與原理\n")
        f.write("專注於干擾特定時刻的關鍵安全訊息，如緊急煞車、變換車道等緊急情況下的 BSM 傳輸，增加交通事故風險。\n\n")
        
        # Simulation 3
        f.write("## 模擬 3: 資源池擾亂攻擊\n\n")
        f.write("### 攻擊目標與原理\n")
        f.write("（盲目型）擾亂整個通信資源池的使用效率，隨機在可用資源上發射干擾，迫使其他車輛選擇較為「嘈雜」的資源，增加訊息碰撞和接收失敗的概率。\n\n")

        # Simulation 4
        f.write("## 模擬 4: 旁路資源耗盡攻擊\n\n")
        f.write("### 攻擊目標與原理\n")
        f.write("這是一種更具智慧的兩階段攻擊。**階段一（誘導）**: 攻擊者策略性地污染大部分可用資源，但刻意保留一小部分「乾淨」的資源，從而製造一個「資源漏斗」，誘導正常車輛的選擇。**階段二（伏擊）**: 在下一週期，攻擊者預測到正常車輛會選用那些被保留的「乾淨」資源，於是搶先一步在這些資源上進行高功率干擾，造成必然的通訊失敗。\n\n")
        
        # Comparison
        f.write("## 攻擊方法比較\n\n")
        f.write("| 攻擊方法 | 攻擊目標 | 技術複雜度 | 影響範圍 | 檢測難度 |\n")
        f.write("|---|---|---|---|---|\n")
        f.write("| 針對性一階 SCI 干擾 | 特定車輛 | 中等 | 局部 | 較高 |\n")
        f.write("| 碰撞關鍵訊息攻擊 | 緊急訊息 | 較低 | 局部 | 高 |\n")
        f.write("| 資源池擾亂攻擊 | 整體資源池 | 較高 | 全局 | 中等 |\n")
        f.write("| 旁路資源耗盡攻擊 | 整體資源池 | 極高 | 全局 | 極高 |\n\n")
        
        # Conclusion
        f.write("## 結論\n\n")
        f.write("通過對多種攻擊方法的模擬與分析，我們發現 NR-V2X 系統在去中心化模式下的資源分配機制存在嚴重安全隱患。攻擊者可利用的手段從簡單的盲目干擾到複雜的、具備預測能力的策略性攻擊，後者（如資源耗盡攻擊）顯示出對系統的巨大威脅，且極難被檢測。\n\n")
        
    print(f"模擬報告已生成: {report_path}")
    return report_path

def main():
    """主函數"""
    attack_dirs = setup_output_dirs()
    
    # I will comment out the old simulations and only run the new one for now.
    # # Run simulation 1
    # sim1_log_dir = run_simulation_1()
    # copy_logs(sim1_log_dir, attack_dirs['simulation_1'])
    
    # # Run simulation 2
    # sim2_log_dir = run_simulation_2()
    # copy_logs(sim2_log_dir, attack_dirs['simulation_2'])
    
    # Run simulation 3
    sim3_log_dir = run_simulation_3()
    copy_logs(sim3_log_dir, attack_dirs['simulation_3'])

    # Run simulation 4
    sim4_log_dir = run_simulation_4()
    copy_logs(sim4_log_dir, attack_dirs['simulation_4'])
    
    # Analyze results and generate report
    report_path = analyze_results(attack_dirs)
    
    print("\n=== 所有模擬完成 ===")
    print(f"結果保存在: {os.path.dirname(attack_dirs['simulation_1']) }")
    print(f"模擬報告: {report_path}")

if __name__ == "__main__":
    main()
