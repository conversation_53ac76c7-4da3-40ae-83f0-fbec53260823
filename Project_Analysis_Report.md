# NR-V2X 模擬器專案分析報告

## 1. 專案概覽

本專案是一個基於 Python 的 **NR-V2X (New Radio Vehicle-to-Everything) 車聯網通訊模擬器**。其主要目標是模擬和評估在 3GPP 定義的 Sidelink Mode 2 (去中心化資源分配模式) 下，車輛間通訊的效能，並特別關注幾種特定網路攻擊對系統穩定性和安全性的影響。

- **技術棧**: 純 Python 3
- **核心場景**: V2X Sidelink Mode 2 (SPS - Semi-Persistent Scheduling)
- **模擬內容**: 車輛移動、訊號傳播、資源感知與選擇、資料傳輸以及多種攻擊者行為。

## 2. 核心架構

專案採用了模組化的設計，將不同職責清晰地分離到各個檔案和目錄中。

- **`run_simulations.py`**: **專案主入口**。負責配置和啟動不同的模擬場景、管理輸出目錄，並在模擬結束後生成一份綜合分析報告。

- **`src/CONFIG.py`**: **中央設定檔**。集中管理所有模擬參數，包括物理環境、通訊參數、SPS 演算法參數以及各類攻擊模型的特定配置。這種設計使得參數調整非常便捷。

- **`src/simulators/`**: **模擬器核心邏輯**。
  - `simulator.py` (`NRV2XSimulator`): 提供基礎的模擬框架，特別是針對「針對性 SCI 干擾攻擊」。
  - `simulator_2.py`, `simulator_3.py`, etc.: 繼承或擴充基礎模擬器，以實現特定的攻擊場景。
  - **核心職責**: 作為時間引擎，以固定的時間步長 (`1ms`) 推進模擬，並在每個時間點觸發所有車輛 (UEs) 的行為。

- **`src/ue.py` (`UE`)**: **模擬的基本單位**。封裝了單一車輛的所有狀態和行為，是整個模擬的核心。它完整實現了 SPS 資源分配演算法。

- **`src/attacker_model/`**: **攻擊者模型庫**。定義了各種攻擊者的行為邏輯，它們通常繼承自 `UE`，並覆寫其 `operate` 方法以實現惡意行為。

- **`src/communication.py`**: **物理層通訊模型**。負責模擬無線訊號的傳播、計算路徑損耗 (Path Loss) 和最關鍵的**訊號雜訊比 (SINR)**，從而判斷一次通訊是否成功。

- **`src/resource_pool.py` (`ResourcePool`)**: **共享資源池**。它是一個全域的資料結構，以時間和頻率為座標，記錄了所有資源的佔用情況。它是實現去中心化資源感知和避免碰撞的基礎。

- **`src/mobility.py`**: **移動模型**。負責管理和更新所有車輛在二維空間中的位置。

- **`logs/` & `output/`**: 分別用於存放模擬過程中的原始日誌和經過整理的最終結果與報告。

## 3. 運作流程

模擬器的工作流程構成一個完整的閉環，精確地模擬了去中心化網路的動態特性：

1.  **啟動**: `run_simulations.py` 根據所選場景，實例化對應的 `Simulator`。
2.  **初始化**: `Simulator` 創建全域的 `ResourcePool` 和 `MobilityManager`，並根據設定初始化所有 `NormalUE` 和 `AttackerUE`。
3.  **時間推進**: `Simulator` 的主迴圈以 `1ms` 為步長推進模擬時間。
4.  **UE 操作**: 在每一個時間步，`Simulator` 遍歷所有 UE，並呼叫其 `operate()` 方法。
5.  **SPS 資源管理**: 在 `operate()` 方法中，UE 根據其內部資源預留計數器 `slrrc` 的狀態，執行以下操作之一：
    - **`slrrc == 0` (需要選擇新資源)**:
        a. **感知 (Sensing)**: UE 透過查詢 `ResourcePool` 來感知過去一段時間內的資源佔用情況。
        b. **獲取候選**: UE 呼叫 `ResourcePool.get_candidate_resources()`，獲取未來選擇窗口內的可用資源列表。此過程會排除掉已被佔用或干擾過強的資源。
        c. **隨機選擇**: UE 從候選列表中隨機選擇一個資源進行預留，並設定 `slrrc` 的初始值。
    - **`slrrc > 0` (已預留資源)**:
        a. **傳輸**: 如果當前時間點是其預留的傳輸時間，UE 會生成 BSM (包含 SCI 和 PSSCH)，並呼叫 `Communication.broadcast_signal()` 進行廣播。
        b. **廣播**: `Communication` 模組計算訊號對其他所有 UE 的接收功率 (RSRP) 和 SINR。
        c. **記錄佔用**: UE 的傳輸行為會透過 `ResourcePool.add_signal()` 被記錄下來。
        d. **接收判定**: 對於每個接收者，如果其計算出的 SINR 高於解碼閾值，則成功接收；否則失敗。成功接收的 UE 會在其本地記錄下這次資源佔用，用於未來的感知。
        e. **計數器遞減**: 傳輸完成後，`slrrc` 遞減。
6.  **循環**: 流程回到步驟 3，直到模擬時間結束。

## 4. 關鍵演算法：SPS 資源分配

SPS (Semi-Persistent Scheduling) 是 Sidelink Mode 2 的核心。其精髓在於每個 UE 都自主地、週期性地預留資源，並在預留期間內重複使用。本專案對其實現的關鍵點如下：

- **感知窗口 (Sensing Window)**: UE "回看" 的時間範圍，用以評估歷史資源使用情況。
- **選擇窗口 (Selection Window)**: UE "前瞻" 的時間範圍，用以尋找未來的可用資源。
- **RSRP 閾值**: 在感知過程中，訊號強度低於此閾值的遠距離傳輸會被忽略，模擬真實物理限制。
- **資源預留計數器 (slrrc)**: UE 選定一個資源後，會預留 `slrrc` 個傳輸週期。這個計數器賦予了資源分配的「半持續性」特點。
- **資源保持機率**: 在一個預留週期結束時，UE 有一定機率會繼續使用當前資源，而不是立即重新選擇，這增加了資源使用的穩定性。

## 5. 物理模型

`communication.py` 模組為模擬提供了物理層的現實基礎。

- **路徑損耗**: 採用**自由空間路徑損耗模型 (FSPL)**，訊號強度隨距離的平方反比衰減。
- **SINR 計算**: 這是決定通訊成敗的黃金標準。
  - **S (Signal)**: 目標訊號的接收功率 (RSRP)。
  - **I (Interference)**: **同一時頻資源**上所有其他訊號的接收功率之和。這是透過查詢 `ResourcePool` 得到的。
  - **N (Noise)**: 根據設定的熱雜訊和頻寬計算出的背景雜訊。
- **解碼閾值**: `CONFIG.py` 中為 SCI 和 PSSCH 分別設定了不同的 SINR 閾值，只有當 SINR 超過閾值時，解碼才能成功。

## 6. 攻擊模型分析

專案實現了三種層次分明、目標各異的攻擊模型：

### 攻擊 1: 針對性一階 SCI 干擾 (Targeted SCI Jamming)

- **原理**: 攻擊者監聽並預測特定受害者的下一次 SCI 傳輸時機，然後在該精確時刻進行大功率干擾。
- **效果**: 阻止受害者的 SCI 被周圍車輛接收。由於 SCI 是宣告資源佔用的「告示」，受害者雖然仍在傳輸資料，但在其他車輛看來，它佔用的資源是「空閒」的，這不僅使其自身訊息無法傳播，還會導致其他車輛選擇該資源而造成碰撞。最終使受害者成為網路中的「幽靈車輛」。

### 攻擊 2: 碰撞關鍵訊息 (Critical Message Collision)

- **原理**: 這是一種**反應式**攻擊。攻擊者平時靜默監聽，一旦偵測到內容被標記為「緊急」的訊息，就立即在同一資源上發射干擾訊號。
- **效果**: 精準地使高價值、時效性強的緊急安全訊息（如緊急煞車警告）傳輸失敗，可能直接導致交通事故。由於其只在關鍵時刻出手，因此更為隱蔽。

### 攻擊 3: 資源池擾亂 (Resource Pool Disruption)

- **原理**: 這是一種**主動式**、**全域性**的攻擊。攻擊者持續地感知網路中的可用「乾淨」資源，並主動在這些資源上發射不大不小的干擾訊號，故意「污染」它們。
- **效果**: 破壞整個資源池的健康度。正常 UE 在選擇資源時，會發現大量看似可用的資源都存在干擾，從而被排除在候選列表之外。這迫使所有 UE 擠在一個更小、更差的資源集裡，導致全域性的訊號碰撞機率顯著增加，系統整體通訊效能下降。
