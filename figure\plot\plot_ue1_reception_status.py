
import pandas as pd
import matplotlib.pyplot as plt
import os
import seaborn as sns

def plot_reception_status_from_ue1():
    """
    Analyzes and plots the packet reception status for NormalUE-1, showing
    whether packets from different source UEs were successfully received.
    """
    # --- 1. File Paths ---
    script_dir = os.path.dirname(os.path.abspath(__file__))
    base_dir = os.path.join(script_dir, '..', '..')
    data_path = os.path.join(
        base_dir,
        'output',
        'nr_v2x_simulator_simulation_results',
        'simulation_1',
        'NormalUE-1.csv'
    )
    output_dir = os.path.join(script_dir)
    output_filename = 'simulation_1_ue1_reception_status.png'
    output_path = os.path.join(output_dir, output_filename)

    os.makedirs(output_dir, exist_ok=True)

    # --- 2. Data Loading and Preprocessing ---
    try:
        df_raw = pd.read_csv(data_path)
    except FileNotFoundError:
        print(f"Error: Data file not found at {data_path}")
        return

    # Filter for PSSCH reception outcomes
    df = df_raw[df_raw['EventType'] == 'PSSCH_RX_OUTCOME'].copy()

    # Convert Time to seconds
    df['Time (s)'] = pd.to_numeric(df['Time_ms'], errors='coerce') / 1000

    # Map Outcome to a binary format (1 for Success, 0 for Failure)
    df['ReceptionSuccess'] = (df['Outcome'] == 'SUCCESS').astype(int)

    # Drop rows with missing essential data
    df.dropna(subset=['Time (s)', 'Related_UE_ID'], inplace=True)
    df.sort_values(by='Time (s)', inplace=True)

    # --- 3. Plotting ---
    plt.style.use('seaborn-v0_8-whitegrid')
    plt.figure(figsize=(16, 8))

    # Create a color palette for the different UEs
    unique_ues = df['Related_UE_ID'].unique()
    palette = sns.color_palette('husl', n_colors=len(unique_ues))
    color_map = dict(zip(unique_ues, palette))

    # Plot reception status for each source UE
    for ue_id, color in color_map.items():
        ue_df = df[df['Related_UE_ID'] == ue_id]
        # Plot reception status for each source UE as a line plot
        plt.plot(
            ue_df['Time (s)'],
            ue_df['ReceptionSuccess'],
            alpha=0.7,
            linestyle='-',
            marker='o', # Add markers to indicate individual packet events
            markersize=4,
            label=f'From {ue_id}',
            color=color
        )

    # --- 4. Finalizing and Saving ---
    plt.title('Packet Reception Status at NormalUE-1', fontsize=18, weight='bold')
    plt.xlabel('Time (s)', fontsize=12)
    plt.ylabel('Reception Outcome (1 = Success, 0 = Failure)', fontsize=12)
    plt.yticks([0, 1], ['Failure', 'Success'])
    plt.ylim(-0.1, 1.2)
    plt.legend(title='Source UE', bbox_to_anchor=(1.02, 1), loc='upper left')
    plt.grid(True, which='both', linestyle='--', linewidth=0.5)
    plt.tight_layout(rect=[0, 0, 0.85, 1]) # Adjust layout for legend

    plt.savefig(output_path, dpi=300)
    print(f"Plot saved to {output_path}")
    # plt.show()

if __name__ == '__main__':
    plot_reception_status_from_ue1()
