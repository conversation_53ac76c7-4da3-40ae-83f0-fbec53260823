# NR-V2X 攻擊方法模擬報告

## 模擬 1: 針對性一階 SCI 干擾攻擊

### 攻擊目標與原理
選擇性地、精確地阻止特定目標車輛的一階 SCI 被其周圍車輛成功接收和解碼，使目標車輛在車聯網中變為「不可見」的「幽靈車輛」。

## 模擬 2: 碰撞關鍵訊息攻擊

### 攻擊目標與原理
專注於干擾特定時刻的關鍵安全訊息，如緊急煞車、變換車道等緊急情況下的 BSM 傳輸，增加交通事故風險。

## 模擬 3: 資源池擾亂攻擊

### 攻擊目標與原理
（盲目型）擾亂整個通信資源池的使用效率，隨機在可用資源上發射干擾，迫使其他車輛選擇較為「嘈雜」的資源，增加訊息碰撞和接收失敗的概率。

## 模擬 4: 旁路資源耗盡攻擊

### 攻擊目標與原理
這是一種更具智慧的兩階段攻擊。**階段一（誘導）**: 攻擊者策略性地污染大部分可用資源，但刻意保留一小部分「乾淨」的資源，從而製造一個「資源漏斗」，誘導正常車輛的選擇。**階段二（伏擊）**: 在下一週期，攻擊者預測到正常車輛會選用那些被保留的「乾淨」資源，於是搶先一步在這些資源上進行高功率干擾，造成必然的通訊失敗。

## 攻擊方法比較

| 攻擊方法 | 攻擊目標 | 技術複雜度 | 影響範圍 | 檢測難度 |
|---|---|---|---|---|
| 針對性一階 SCI 干擾 | 特定車輛 | 中等 | 局部 | 較高 |
| 碰撞關鍵訊息攻擊 | 緊急訊息 | 較低 | 局部 | 高 |
| 資源池擾亂攻擊 | 整體資源池 | 較高 | 全局 | 中等 |
| 旁路資源耗盡攻擊 | 整體資源池 | 極高 | 全局 | 極高 |

## 結論

通過對多種攻擊方法的模擬與分析，我們發現 NR-V2X 系統在去中心化模式下的資源分配機制存在嚴重安全隱患。攻擊者可利用的手段從簡單的盲目干擾到複雜的、具備預測能力的策略性攻擊，後者（如資源耗盡攻擊）顯示出對系統的巨大威脅，且極難被檢測。

