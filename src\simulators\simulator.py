# simulator.py
import simpy
import random
import math
import os

# Import local modules
from src.ue import VehicleUE, AttackerUE, DEFAULT_PRSRP
from src.resource_pool import ResourcePool
from src.communication import CommunicationModel
from src.mobility import MobilityModel, MODEL_LINEAR, MOD<PERSON>_RANDOM_WAYPOINT
from src.logger import Logger # Assuming Logger class is in logger.py
from src.CONFIG import *

# Define fieldnames for UE-specific logs, can be moved to CONFIG.py if preferred
# UE_SPECIFIC_LOG_FIELDNAMES = [
#     "Time_ms", "EventType", "Resource_Details","SLRRC", "SCI_Content_Abstract", 
#     "PSSCH_Content_Abstract", "RSRP_dBm", "SINR_dB", "Is_Selected_By_Self", 
#     "Is_Transmission", "Related_UE_ID", "Outcome", "Raw_Candidate_Resources", "Details"
# ]

class NRV2XSimulator:
    def __init__(self, simulation_index, num_normal_ues=5, num_attacker_ues=1, attacker_victim_id="NormalUE-0"):
        self.env = simpy.Environment()

        # Initialize Global Logger first
        if not os.path.exists(LOG_DIR):
            os.makedirs(LOG_DIR)
        self.global_logger = Logger(filename=LOG_FILENAME, log_dir=LOG_DIR)

        # Prepare directory for UE-specific logs
        self.ue_log_dir = os.path.join(LOG_DIR, "ue_logs")
        self.ue_log_dir = os.path.join(LOG_DIR, "ue_logs", simulation_index)
        if not os.path.exists(self.ue_log_dir):
            os.makedirs(self.ue_log_dir)

        self.resource_pool = ResourcePool(self.env, self.global_logger)
        self.comm_model = CommunicationModel(self.global_logger, self.resource_pool)
        
        self.ues_objects_dict = {} 
        self.mobility_model = MobilityModel(self.global_logger, self.env, self.ues_objects_dict, area_dimensions=(AREA_WIDTH, AREA_HEIGHT))

        self.normal_ues = []
        self.attacker_ues = []
        self.all_ues = [] 
        self.ue_loggers = [] # To store UE-specific logger instances

        self.num_normal_ues = num_normal_ues
        self.num_attacker_ues = num_attacker_ues
        self.attacker_victim_id = attacker_victim_id

        self.global_logger.log_event(Time_ms=self.env.now, EventType="SIM_INIT_START", UE_ID="System", Details=f"Duration: {SIM_DURATION_MS}ms, NormalUEs: {self.num_normal_ues}, Attackers: {NUM_ATTACKER_UES}")
        print(f"NR-V2X Mode 2 Simulator Initialized. Global logging to: {os.path.join(LOG_DIR, LOG_FILENAME)}")
        print(f"UE-specific logs will be in: {self.ue_log_dir}")

    def setup_scenario(self):
        self.global_logger.log_event(Time_ms=self.env.now, EventType="SCENARIO_SETUP_START", UE_ID="System")
        print(f"Time {self.env.now}: Setting up scenario...")

        for i in range(self.num_normal_ues):
            ue_id = f"NormalUE-{i}"
            initial_pos = (random.uniform(0, 100), random.uniform(0, AREA_HEIGHT))
            initial_dir_deg = 0
            initial_speed_mps = random.uniform(8, 15)
            
            # Create UE-specific logger
            ue_logger_instance = Logger(filename=f"{ue_id}.csv", log_dir=self.ue_log_dir, fieldnames=UE_SPECIFIC_LOG_FIELDNAMES)
            self.ue_loggers.append(ue_logger_instance)
            
            ue = VehicleUE(self.env, ue_id, self.resource_pool, self.comm_model, self.global_logger, self.mobility_model,
                           initial_pos=initial_pos, initial_speed=initial_speed_mps,
                           prsvp=PRSVP, ue_specific_logger=ue_logger_instance)
            self.normal_ues.append(ue)
            self.all_ues.append(ue)
            self.ues_objects_dict[ue_id] = ue
            self.mobility_model.initialize_ue_state(ue_id, model_type=MODEL_LINEAR, 
                                                   initial_pos=initial_pos, 
                                                   initial_speed_mps=initial_speed_mps, 
                                                   initial_direction_deg=initial_dir_deg)
            self.global_logger.log_event(Time_ms=self.env.now, EventType="UE_CREATED", UE_ID=ue_id, Details="Type: Normal")
            print(f"  {ue_id} created.")

        for i in range(NUM_ATTACKER_UES):
            attacker_id = f"AttackerUE-{i}"
            initial_pos = (random.uniform(0, 100), random.uniform(0, AREA_HEIGHT))
            initial_dir_deg = 90
            initial_speed_mps = random.uniform(10, 12)

            victim_to_assign = None
            if ATTACKER_VICTIM_ID and ATTACKER_VICTIM_ID in self.ues_objects_dict:
                victim_to_assign = ATTACKER_VICTIM_ID
            elif self.normal_ues:
                victim_to_assign = self.normal_ues[0].ue_id
            
            # Create UE-specific logger for attacker
            attacker_logger_instance = Logger(filename=f"{attacker_id}.csv", log_dir=self.ue_log_dir, fieldnames=UE_SPECIFIC_LOG_FIELDNAMES)
            self.ue_loggers.append(attacker_logger_instance)
            attacker = AttackerUE(self.env, attacker_id, self.resource_pool, self.comm_model, self.global_logger, self.mobility_model,
                                initial_pos=initial_pos, initial_speed=initial_speed_mps,
                                victim_ue_id=victim_to_assign, ue_specific_logger=attacker_logger_instance)
            self.attacker_ues.append(attacker)
            self.all_ues.append(attacker)
            self.ues_objects_dict[attacker_id] = attacker
            self.mobility_model.initialize_ue_state(attacker_id, model_type=MODEL_LINEAR, 
                                                     initial_pos=initial_pos, 
                                                     initial_speed_mps=initial_speed_mps, 
                                                     initial_direction_deg=initial_dir_deg)
            self.global_logger.log_event(Time_ms=self.env.now, EventType="UE_CREATED", UE_ID=attacker_id, Details=f"Type: Attacker, Target: {victim_to_assign if victim_to_assign else 'Dynamic'}")
            print(f"  {attacker_id} created, targeting {victim_to_assign if victim_to_assign else 'Dynamic'}.")

        for ue in self.all_ues:
            ue.all_other_ues = {other_ue.ue_id: other_ue for other_ue in self.all_ues if other_ue.ue_id != ue.ue_id}
            ue.comm_model = self.comm_model 
            ue.resource_pool = self.resource_pool

        self.global_logger.log_event(Time_ms=self.env.now, EventType="SCENARIO_SETUP_END", UE_ID="System")
        print(f"Time {self.env.now}: Scenario setup complete. Total UEs: {len(self.all_ues)}")

    def run_simulation(self):
        self.global_logger.log_event(Time_ms=self.env.now, EventType="SIM_RUN_START", UE_ID="System")
        print(f"Time {self.env.now}: Starting simulation run...")
        
        self.setup_scenario()
        self.env.process(self.update_mobility_periodically())
        
        for ue in self.all_ues:
            self.env.process(ue.receiver_process())

        self.env.run(until=SIM_DURATION_MS)
        
        self.global_logger.log_event(Time_ms=self.env.now, EventType="SIM_RUN_END", UE_ID="System", Details=f"Simulation finished at {self.env.now:.2f} ms")
        print(f"Simulation finished at time {self.env.now:.2f} ms")
        self.global_logger.close()
        for ue_logger in self.ue_loggers:
            ue_logger.close()

    def update_mobility_periodically(self):
        last_update_time = self.env.now
        while True:
            yield self.env.timeout(MOBILITY_UPDATE_INTERVAL_MS)
            current_time = self.env.now
            self.mobility_model.update_all_positions(current_time, last_update_time)
            if current_time % (MOBILITY_UPDATE_INTERVAL_MS * 5) == 0: 
                 for ue_id, ue_obj in self.ues_objects_dict.items():
                      self.global_logger.log_event(Time_ms=current_time, EventType="MOBILITY_SNAPSHOT", UE_ID=ue_id, Position=ue_obj.pos, Details=f"Speed:{ue_obj.speed_mps:.1f}m/s")
            last_update_time = current_time

if __name__ == "__main__":
    simulator = NRV2XSimulator()
    try:
        simulator.run_simulation()
    except Exception as e:
        print(f"Critical error during simulation: {e}")
        if hasattr(simulator, 'global_logger') and simulator.global_logger:
            simulator.global_logger.log_event(Time_ms=simulator.env.now if hasattr(simulator, 'env') else -1, 
                                       EventType="SIM_CRITICAL_ERROR", UE_ID="System", Details=str(e))
            simulator.global_logger.close()
        for ue_logger in getattr(simulator, 'ue_loggers', []):
            if ue_logger:
                ue_logger.close()
        raise
    
    print("--- Simulation Main Script Finished ---")

